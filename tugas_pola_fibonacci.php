<?php
echo "=== TUGAS PEMROGRAMAN PHP ===\n\n";

// TUGAS 1: Program untuk mencetak pola bintang
echo "1. POLA BINTANG:\n\n";

echo "a. Pola Segitiga Naik:\n";
// Pola a: segitiga naik
for ($i = 1; $i <= 12; $i++) {
    for ($j = 1; $j <= $i; $j++) {
        echo "*";
    }
    echo "\n";
}

echo "\nb. Pola Segitiga Turun:\n";
// Pola b: segitiga turun
for ($i = 10; $i >= 1; $i--) {
    // Cetak spasi untuk rata tengah
    for ($j = 1; $j <= (10 - $i); $j++) {
        echo " ";
    }
    // Cetak bintang
    for ($k = 1; $k <= $i; $k++) {
        echo "*";
    }
    echo "\n";
}

echo "\n" . str_repeat("=", 50) . "\n\n";

// TUGAS 2: Program untuk mencetak deret Fibonacci
echo "2. DERET FIBONACCI:\n\n";

function fibonacci($n) {
    $fib = array();
    
    if ($n >= 1) $fib[0] = 0;
    if ($n >= 2) $fib[1] = 1;
    
    for ($i = 2; $i < $n; $i++) {
        $fib[$i] = $fib[$i-1] + $fib[$i-2];
    }
    
    return $fib;
}

// Mencetak 15 angka pertama deret Fibonacci
$jumlah_angka = 15;
$deret_fibonacci = fibonacci($jumlah_angka);

echo "Deret Fibonacci ($jumlah_angka angka pertama):\n";
echo implode(", ", $deret_fibonacci) . "\n\n";

// Menampilkan dengan format yang lebih rapi
echo "Penjelasan deret Fibonacci:\n";
for ($i = 0; $i < count($deret_fibonacci); $i++) {
    if ($i == 0) {
        echo "F($i) = {$deret_fibonacci[$i]} (angka pertama)\n";
    } elseif ($i == 1) {
        echo "F($i) = {$deret_fibonacci[$i]} (angka kedua)\n";
    } else {
        $prev1 = $deret_fibonacci[$i-1];
        $prev2 = $deret_fibonacci[$i-2];
        echo "F($i) = F(" . ($i-1) . ") + F(" . ($i-2) . ") = $prev1 + $prev2 = {$deret_fibonacci[$i]}\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Program selesai!\n";
?>
